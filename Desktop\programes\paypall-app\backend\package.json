{"name": "paypal-app-backend", "version": "1.0.0", "description": "Backend server for PayPal payment application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["paypal", "payment", "express", "api"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "axios": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1"}}