const express = require('express');
const axios = require('axios');
const router = express.Router();

// PayPal API configuration
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID;
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET;
const PAYPAL_ENVIRONMENT = process.env.PAYPAL_ENVIRONMENT || 'sandbox';
const PAYPAL_BASE_URL = PAYPAL_ENVIRONMENT === 'production' 
  ? 'https://api.paypal.com' 
  : 'https://api.sandbox.paypal.com';

// Get PayPal access token
async function getPayPalAccessToken() {
  try {
    const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`).toString('base64');
    
    const response = await axios.post(`${PAYPAL_BASE_URL}/v1/oauth2/token`, 
      'grant_type=client_credentials',
      {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    return response.data.access_token;
  } catch (error) {
    console.error('Error getting PayPal access token:', error.response?.data || error.message);
    throw new Error('Failed to get PayPal access token');
  }
}

// Create PayPal order
router.post('/create-paypal-order', async (req, res) => {
  try {
    const { amount, currency = 'USD', items = [] } = req.body;
    
    if (!amount || amount <= 0) {
      return res.status(400).json({ error: 'Invalid amount' });
    }

    const accessToken = await getPayPalAccessToken();
    
    const orderData = {
      intent: 'CAPTURE',
      purchase_units: [{
        amount: {
          currency_code: currency,
          value: amount.toString()
        },
        items: items.length > 0 ? items : [{
          name: 'Payment',
          quantity: '1',
          unit_amount: {
            currency_code: currency,
            value: amount.toString()
          }
        }]
      }],
      application_context: {
        return_url: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/payment/success`,
        cancel_url: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/payment/cancel`,
        shipping_preference: 'NO_SHIPPING',
        user_action: 'PAY_NOW'
      }
    };

    const response = await axios.post(`${PAYPAL_BASE_URL}/v2/checkout/orders`, orderData, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    res.json({ 
      orderID: response.data.id,
      status: response.data.status,
      links: response.data.links
    });
  } catch (error) {
    console.error('Error creating PayPal order:', error.response?.data || error.message);
    res.status(500).json({ 
      error: 'Failed to create PayPal order',
      details: error.response?.data || error.message
    });
  }
});

// Capture PayPal order
router.post('/capture-paypal-order', async (req, res) => {
  try {
    const { orderID } = req.body;
    
    if (!orderID) {
      return res.status(400).json({ error: 'Order ID is required' });
    }

    const accessToken = await getPayPalAccessToken();
    
    const response = await axios.post(
      `${PAYPAL_BASE_URL}/v2/checkout/orders/${orderID}/capture`,
      {},
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    res.json({
      orderID: response.data.id,
      status: response.data.status,
      paymentDetails: response.data.purchase_units[0].payments.captures[0]
    });
  } catch (error) {
    console.error('Error capturing PayPal order:', error.response?.data || error.message);
    res.status(500).json({ 
      error: 'Failed to capture PayPal order',
      details: error.response?.data || error.message
    });
  }
});

// Mock credit card payment endpoint
router.post('/process-credit-card', async (req, res) => {
  try {
    const { cardNumber, expiryDate, cvv, amount, currency = 'USD' } = req.body;
    
    // Basic validation
    if (!cardNumber || !expiryDate || !cvv || !amount) {
      return res.status(400).json({ error: 'Missing required card details' });
    }
    
    if (amount <= 0) {
      return res.status(400).json({ error: 'Invalid amount' });
    }

    // Mock processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock success/failure based on card number
    const isSuccess = !cardNumber.startsWith('****************'); // Mock failure card
    
    if (isSuccess) {
      res.json({
        success: true,
        transactionId: `cc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        amount,
        currency,
        status: 'completed',
        message: 'Payment processed successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: 'Payment declined',
        message: 'Your card was declined. Please try a different payment method.'
      });
    }
  } catch (error) {
    console.error('Error processing credit card payment:', error);
    res.status(500).json({ 
      error: 'Failed to process credit card payment',
      message: 'An error occurred while processing your payment'
    });
  }
});

// Get payment status
router.get('/status/:transactionId', (req, res) => {
  const { transactionId } = req.params;
  
  // Mock payment status lookup
  res.json({
    transactionId,
    status: 'completed',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
