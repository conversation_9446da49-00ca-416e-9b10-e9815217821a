import React, { useState } from 'react'
import { usePayment } from '../context/PaymentContext'
import CartSummary from '../components/CartSummary'
import PayPalButton from '../components/PayPalButton'
import CreditCardForm from '../components/CreditCardForm'
import LoadingSpinner from '../components/LoadingSpinner'

const PaymentPage = () => {
  const { state, setPaymentMethod } = usePayment()
  const [selectedMethod, setSelectedMethod] = useState(null)

  const handleMethodSelect = (method) => {
    setSelectedMethod(method)
    setPaymentMethod(method)
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Complete Your Purchase
        </h1>
        <p className="text-lg text-gray-600">
          Choose your preferred payment method to continue
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Cart Summary */}
        <div className="order-2 lg:order-1">
          <CartSummary />
        </div>

        {/* Payment Methods */}
        <div className="order-1 lg:order-2">
          <div className="bg-white rounded-2xl shadow-xl p-6 lg:p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Payment Method
            </h2>

            {/* Payment Method Selection */}
            <div className="space-y-4 mb-8">
              {/* PayPal Option */}
              <div 
                className={`border-2 rounded-xl p-4 cursor-pointer transition-all duration-200 ${
                  selectedMethod === 'paypal' 
                    ? 'border-paypal-blue bg-blue-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleMethodSelect('paypal')}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-paypal-blue rounded-lg flex items-center justify-center">
                      <svg className="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.26-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81.678.773 1.016 1.638 1.074 2.607z"/>
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">PayPal</h3>
                      <p className="text-sm text-gray-500">Pay with your PayPal account</p>
                    </div>
                  </div>
                  <div className={`w-5 h-5 rounded-full border-2 ${
                    selectedMethod === 'paypal' 
                      ? 'border-paypal-blue bg-paypal-blue' 
                      : 'border-gray-300'
                  }`}>
                    {selectedMethod === 'paypal' && (
                      <div className="w-full h-full rounded-full bg-white scale-50"></div>
                    )}
                  </div>
                </div>
              </div>

              {/* Credit Card Option */}
              <div 
                className={`border-2 rounded-xl p-4 cursor-pointer transition-all duration-200 ${
                  selectedMethod === 'credit-card' 
                    ? 'border-indigo-500 bg-indigo-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleMethodSelect('credit-card')}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Credit Card</h3>
                      <p className="text-sm text-gray-500">Pay with Visa, Mastercard, or Amex</p>
                    </div>
                  </div>
                  <div className={`w-5 h-5 rounded-full border-2 ${
                    selectedMethod === 'credit-card' 
                      ? 'border-indigo-500 bg-indigo-500' 
                      : 'border-gray-300'
                  }`}>
                    {selectedMethod === 'credit-card' && (
                      <div className="w-full h-full rounded-full bg-white scale-50"></div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Form */}
            {state.loading && <LoadingSpinner />}
            
            {selectedMethod === 'paypal' && !state.loading && (
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-sm text-blue-800">
                    You will be redirected to PayPal to complete your payment securely.
                  </p>
                </div>
                <PayPalButton />
              </div>
            )}

            {selectedMethod === 'credit-card' && !state.loading && (
              <CreditCardForm />
            )}

            {!selectedMethod && (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <p className="text-gray-500">Select a payment method to continue</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default PaymentPage
