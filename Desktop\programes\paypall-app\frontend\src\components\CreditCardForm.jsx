import React, { useState } from 'react'
import { usePayment } from '../context/PaymentContext'
import { useNavigate } from 'react-router-dom'
import { processCreditCardPayment } from '../services/paymentService'

const CreditCardForm = () => {
  const { state, startPayment, paymentSuccess, paymentError } = usePayment()
  const navigate = useNavigate()
  
  const [formData, setFormData] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    billingAddress: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'US'
    }
  })

  const [errors, setErrors] = useState({})

  const handleInputChange = (e) => {
    const { name, value } = e.target
    
    if (name.startsWith('billing.')) {
      const field = name.split('.')[1]
      setFormData(prev => ({
        ...prev,
        billingAddress: {
          ...prev.billingAddress,
          [field]: value
        }
      }))
    } else {
      let formattedValue = value
      
      // Format card number
      if (name === 'cardNumber') {
        formattedValue = value.replace(/\s/g, '').replace(/(.{4})/g, '$1 ').trim()
        if (formattedValue.length > 19) formattedValue = formattedValue.slice(0, 19)
      }
      
      // Format expiry date
      if (name === 'expiryDate') {
        formattedValue = value.replace(/\D/g, '').replace(/(\d{2})(\d)/, '$1/$2')
        if (formattedValue.length > 5) formattedValue = formattedValue.slice(0, 5)
      }
      
      // Format CVV
      if (name === 'cvv') {
        formattedValue = value.replace(/\D/g, '').slice(0, 4)
      }
      
      setFormData(prev => ({
        ...prev,
        [name]: formattedValue
      }))
    }
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.cardholderName.trim()) {
      newErrors.cardholderName = 'Cardholder name is required'
    }
    
    if (!formData.cardNumber.replace(/\s/g, '')) {
      newErrors.cardNumber = 'Card number is required'
    } else if (formData.cardNumber.replace(/\s/g, '').length < 13) {
      newErrors.cardNumber = 'Invalid card number'
    }
    
    if (!formData.expiryDate) {
      newErrors.expiryDate = 'Expiry date is required'
    } else if (!/^\d{2}\/\d{2}$/.test(formData.expiryDate)) {
      newErrors.expiryDate = 'Invalid expiry date format (MM/YY)'
    }
    
    if (!formData.cvv) {
      newErrors.cvv = 'CVV is required'
    } else if (formData.cvv.length < 3) {
      newErrors.cvv = 'Invalid CVV'
    }
    
    if (!formData.billingAddress.street.trim()) {
      newErrors['billing.street'] = 'Street address is required'
    }
    
    if (!formData.billingAddress.city.trim()) {
      newErrors['billing.city'] = 'City is required'
    }
    
    if (!formData.billingAddress.state.trim()) {
      newErrors['billing.state'] = 'State is required'
    }
    
    if (!formData.billingAddress.zipCode.trim()) {
      newErrors['billing.zipCode'] = 'ZIP code is required'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }
    
    try {
      startPayment()
      
      // Calculate total from cart
      const subtotal = state.cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0)
      const tax = subtotal * 0.08
      const total = subtotal + tax

      const response = await processCreditCardPayment({
        cardNumber: formData.cardNumber.replace(/\s/g, ''),
        expiryDate: formData.expiryDate,
        cvv: formData.cvv,
        cardholderName: formData.cardholderName,
        amount: total.toFixed(2),
        currency: state.cart.currency,
        billingAddress: formData.billingAddress
      })

      if (response.success) {
        paymentSuccess({
          transactionId: response.transactionId
        })

        navigate('/payment/success', {
          state: {
            transactionId: response.transactionId,
            amount: response.amount,
            currency: response.currency
          }
        })
      } else {
        paymentError(response.message || 'Payment failed')
      }
    } catch (error) {
      console.error('Credit card payment error:', error)
      paymentError(error.message || 'Payment processing failed')
    }
  }

  const getCardType = (number) => {
    const cleanNumber = number.replace(/\s/g, '')
    if (cleanNumber.startsWith('4')) return 'visa'
    if (cleanNumber.startsWith('5') || cleanNumber.startsWith('2')) return 'mastercard'
    if (cleanNumber.startsWith('3')) return 'amex'
    return 'generic'
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Card Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Card Information</h3>
        
        {/* Cardholder Name */}
        <div>
          <label htmlFor="cardholderName" className="block text-sm font-medium text-gray-700 mb-2">
            Cardholder Name
          </label>
          <input
            type="text"
            id="cardholderName"
            name="cardholderName"
            value={formData.cardholderName}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
              errors.cardholderName ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="John Doe"
          />
          {errors.cardholderName && (
            <p className="mt-1 text-sm text-red-600">{errors.cardholderName}</p>
          )}
        </div>

        {/* Card Number */}
        <div>
          <label htmlFor="cardNumber" className="block text-sm font-medium text-gray-700 mb-2">
            Card Number
          </label>
          <div className="relative">
            <input
              type="text"
              id="cardNumber"
              name="cardNumber"
              value={formData.cardNumber}
              onChange={handleInputChange}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                errors.cardNumber ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="1234 5678 9012 3456"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {getCardType(formData.cardNumber) === 'visa' && (
                <div className="text-blue-600 font-bold text-sm">VISA</div>
              )}
              {getCardType(formData.cardNumber) === 'mastercard' && (
                <div className="text-red-600 font-bold text-sm">MC</div>
              )}
              {getCardType(formData.cardNumber) === 'amex' && (
                <div className="text-green-600 font-bold text-sm">AMEX</div>
              )}
            </div>
          </div>
          {errors.cardNumber && (
            <p className="mt-1 text-sm text-red-600">{errors.cardNumber}</p>
          )}
        </div>

        {/* Expiry Date and CVV */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="expiryDate" className="block text-sm font-medium text-gray-700 mb-2">
              Expiry Date
            </label>
            <input
              type="text"
              id="expiryDate"
              name="expiryDate"
              value={formData.expiryDate}
              onChange={handleInputChange}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                errors.expiryDate ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="MM/YY"
            />
            {errors.expiryDate && (
              <p className="mt-1 text-sm text-red-600">{errors.expiryDate}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="cvv" className="block text-sm font-medium text-gray-700 mb-2">
              CVV
            </label>
            <input
              type="text"
              id="cvv"
              name="cvv"
              value={formData.cvv}
              onChange={handleInputChange}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                errors.cvv ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="123"
            />
            {errors.cvv && (
              <p className="mt-1 text-sm text-red-600">{errors.cvv}</p>
            )}
          </div>
        </div>
      </div>

      {/* Billing Address */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Billing Address</h3>

        <div>
          <label htmlFor="billing.street" className="block text-sm font-medium text-gray-700 mb-2">
            Street Address
          </label>
          <input
            type="text"
            id="billing.street"
            name="billing.street"
            value={formData.billingAddress.street}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
              errors['billing.street'] ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="123 Main Street"
          />
          {errors['billing.street'] && (
            <p className="mt-1 text-sm text-red-600">{errors['billing.street']}</p>
          )}
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="billing.city" className="block text-sm font-medium text-gray-700 mb-2">
              City
            </label>
            <input
              type="text"
              id="billing.city"
              name="billing.city"
              value={formData.billingAddress.city}
              onChange={handleInputChange}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                errors['billing.city'] ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="New York"
            />
            {errors['billing.city'] && (
              <p className="mt-1 text-sm text-red-600">{errors['billing.city']}</p>
            )}
          </div>

          <div>
            <label htmlFor="billing.state" className="block text-sm font-medium text-gray-700 mb-2">
              State
            </label>
            <input
              type="text"
              id="billing.state"
              name="billing.state"
              value={formData.billingAddress.state}
              onChange={handleInputChange}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                errors['billing.state'] ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="NY"
            />
            {errors['billing.state'] && (
              <p className="mt-1 text-sm text-red-600">{errors['billing.state']}</p>
            )}
          </div>
        </div>

        <div>
          <label htmlFor="billing.zipCode" className="block text-sm font-medium text-gray-700 mb-2">
            ZIP Code
          </label>
          <input
            type="text"
            id="billing.zipCode"
            name="billing.zipCode"
            value={formData.billingAddress.zipCode}
            onChange={handleInputChange}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
              errors['billing.zipCode'] ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="10001"
          />
          {errors['billing.zipCode'] && (
            <p className="mt-1 text-sm text-red-600">{errors['billing.zipCode']}</p>
          )}
        </div>
      </div>

      {/* Error Display */}
      {state.payment.error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <svg className="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <div>
              <p className="text-sm font-medium text-red-800">Payment Error</p>
              <p className="text-sm text-red-600">{state.payment.error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Submit Button */}
      <button
        type="submit"
        disabled={state.loading}
        className={`w-full py-4 px-6 rounded-lg font-semibold text-white transition-all duration-200 ${
          state.loading
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-indigo-600 hover:bg-indigo-700 btn-hover-effect'
        }`}
      >
        {state.loading ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full loading-spinner"></div>
            <span>Processing Payment...</span>
          </div>
        ) : (
          `Pay $${(() => {
            const subtotal = state.cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0)
            const tax = subtotal * 0.08
            const total = subtotal + tax
            return total.toFixed(2)
          })()} ${state.cart.currency}`
        )}
      </button>

      {/* Test Card Info */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-yellow-800 mb-2">Test Card Numbers:</h4>
        <div className="text-xs text-yellow-700 space-y-1">
          <p>• Success: 4111 1111 1111 1111</p>
          <p>• Decline: 4000 0000 0000 0002</p>
          <p>• Use any future expiry date and any 3-digit CVV</p>
        </div>
      </div>
    </form>
  )
}

export default CreditCardForm
